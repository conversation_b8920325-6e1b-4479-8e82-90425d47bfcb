<template>
  <div class="app-container"></div>
  <h1>请对本次服务评分</h1>
  <Rating @update-rating="handleRating" />
  <p v-if="rating">您的评分是：{{ rating }}</p>
  <p>感谢您的评价！</p>
</template>

<script setup>
import { ref } from 'vue'
import Rating from './components/Rating.vue'

const rating = ref(0)

const handleRating = (newRating) => {
  rating.value = newRating
}
</script>

<style lang="scss" scoped>
.app-container {
  max-width: 600px;
  margin: auto;
  text-align: center;
  font-family: Arial, sans-serif;
}

p {
  font-size: 18px;
  color: #333;
}
</style>
