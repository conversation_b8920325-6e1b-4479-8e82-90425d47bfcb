<template>
  <div class="rating-container">
    <span v-for="index in 5" :key="index" class="star" @click="setStar(index)">
      {{ rating >= index ? '★' : '☆' }}
    </span>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const rating = ref(0)
const emits = defineEmits(['update-rating'])

const setStar = (index) => {
  rating.value = index
  emits('update-rating', index)
}
</script>

<style lang="scss" scoped>
.rating-container {
  display: flex;
  font-size: 24px;
  cursor: pointer;
}

.star {
  margin-right: 5px;
  color: gold;
}

.star:hover {
  color: orange;
}
</style>
